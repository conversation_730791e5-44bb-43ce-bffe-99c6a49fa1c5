-- liquibase formatted sql

-- changeset redmi:*************-1
CREATE TABLE Configuration (ConfigurationId INT AUTO_INCREMENT NOT NULL, IsActive BIT DEFAULT 1 NOT NULL, Category VARCHAR(50) NOT NULL, CONSTRAINT PK_CONFIGURATION PRIMARY KEY (ConfigurationId));

-- changeset redmi:*************-2
CREATE TABLE ConfigurationValues (ConfigurationValuesId INT AUTO_INCREMENT NOT NULL, Name VARCHAR(100) NOT NULL, Value VARCHAR(65000) NOT NULL, IsSecret BIT DEFAULT 0 NOT NULL, ConfigurationId INT NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, InputType VARCHAR(255) DEFAULT 'input' NOT NULL, CONSTRAINT PK_CONFIGURATIONVALUES PRIMARY KEY (ConfigurationValuesId));

-- changeset redmi:*************-3
CREATE TABLE Image (ImageId INT AUTO_INCREMENT NOT NULL, ImageName VARCHAR(255) NOT NULL, ContentType VARCHAR(255) DEFAULT 'image/png' NOT NULL, ImageBinary LONGBLOB NOT NULL, CONSTRAINT PK_IMAGE PRIMARY KEY (ImageId));

-- changeset redmi:*************-4
CREATE TABLE `Organization` (OrganizationId INT AUTO_INCREMENT NOT NULL, Name LONGTEXT NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, IsActive BIT NOT NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, AllowSubOrg BIT DEFAULT 0 NOT NULL, AllowWhiteLabel BIT DEFAULT 0 NOT NULL, ParentOrganizationId INT DEFAULT 0 NULL, IsVerified BIT DEFAULT 0 NOT NULL, IsPartner BIT DEFAULT 0 NOT NULL, CONSTRAINT PK_ORGANIZATION PRIMARY KEY (OrganizationId));

-- changeset redmi:*************-5
CREATE TABLE OrganizationMapping (Id INT AUTO_INCREMENT NOT NULL, OrganizationId INT NOT NULL, Application ENUM('CloudInfra', 'ConnectWise') NOT NULL, PrimaryId VARCHAR(255) NOT NULL, PrimaryName VARCHAR(255) NULL, SecondaryId VARCHAR(255) NULL, SecondaryName VARCHAR(255) NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, CONSTRAINT PK_ORGANIZATIONMAPPING PRIMARY KEY (Id));

-- changeset redmi:*************-6
CREATE TABLE `Role` (RoleId INT AUTO_INCREMENT NOT NULL, Name LONGTEXT NULL, OrganizationId INT DEFAULT NULL NULL, Permissions BINARY(20) DEFAULT NULL NULL, AvailableTo INT DEFAULT NULL NULL, IsRestricted BIT DEFAULT 0 NULL, `Description` LONGTEXT NULL, CONSTRAINT PK_ROLE PRIMARY KEY (RoleId));

-- changeset redmi:*************-7
CREATE TABLE User (UserId INT AUTO_INCREMENT NOT NULL, FirstName VARCHAR(255) NULL, LastName VARCHAR(255) NULL, Email VARCHAR(255) NULL, Password VARCHAR(255) NULL, IsActive BIT DEFAULT 1 NOT NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, CONSTRAINT PK_USER PRIMARY KEY (UserId));

-- changeset redmi:*************-8
CREATE TABLE User_Organization_Mapping (MappingId INT AUTO_INCREMENT NOT NULL, UserId INT DEFAULT NULL NULL, OrganizationId INT DEFAULT NULL NULL, RoleId INT DEFAULT NULL NULL, CreatedBy INT DEFAULT NULL NULL, CreatedDate datetime DEFAULT '0001-01-01 00:00:00' NOT NULL, IsActive BIT DEFAULT 0 NOT NULL, UpdatedBy INT DEFAULT NULL NULL, UpdatedDate datetime DEFAULT NULL NULL, IsDefault BIT DEFAULT 0 NOT NULL, IsApproved BIT DEFAULT 0 NOT NULL, CONSTRAINT PK_USER_ORGANIZATION_MAPPING PRIMARY KEY (MappingId));

-- changeset redmi:*************-9
CREATE TABLE WhiteLabel (WhiteLabelId INT AUTO_INCREMENT NOT NULL, OrganizationId INT NOT NULL, PortalName VARCHAR(255) NULL, DomainName VARCHAR(255) NULL, PrimaryColor VARCHAR(20) NULL, SecondaryColor VARCHAR(20) NULL, LoginLogo INT DEFAULT NULL NULL, MainPortalLogo INT DEFAULT NULL NULL, BannerLogo INT DEFAULT NULL NULL, NavigationLogo INT DEFAULT NULL NULL, FavIcon INT DEFAULT NULL NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, IsActive BIT DEFAULT 1 NULL, AdaptiveCloudHostname VARCHAR(255) NULL, CONSTRAINT PK_WHITELABEL PRIMARY KEY (WhiteLabelId));

-- changeset redmi:*************-10
CREATE TABLE ac_cw_account_map (id INT AUTO_INCREMENT NOT NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NULL, cwCompanyId INT DEFAULT NULL NULL, cwCompanyName VARCHAR(255) NULL, cwCompanyIdentifier VARCHAR(80) NULL, cwAgreementId INT DEFAULT NULL NULL, cwAgreementName VARCHAR(80) NULL, enabled TINYINT DEFAULT 0 NOT NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, acType VARCHAR(20) DEFAULT 'Account' NOT NULL, billingStartDate date DEFAULT NULL NULL, CONSTRAINT PK_AC_CW_ACCOUNT_MAP PRIMARY KEY (id), UNIQUE (acId));

-- changeset redmi:*************-11
CREATE TABLE ac_cw_product_map (id INT AUTO_INCREMENT NOT NULL, usageType VARCHAR(20) NULL, cwproductid INT DEFAULT NULL NULL, cwproductname VARCHAR(40) NULL, label VARCHAR(40) NULL, units VARCHAR(255) NULL, `description` VARCHAR(255) NULL, prorate TINYINT DEFAULT 0 NULL, bandwidthgb INT DEFAULT NULL NULL, tiered TINYINT DEFAULT 0 NULL, valuefn VARCHAR(80) NULL, CONSTRAINT PK_AC_CW_PRODUCT_MAP PRIMARY KEY (id));

-- changeset redmi:*************-12
CREATE TABLE ac_cw_vm_map (id INT AUTO_INCREMENT NOT NULL, acId VARCHAR(36) NULL, acName VARCHAR(255) NULL, acType VARCHAR(20) NULL, productMapId INT NOT NULL, priority INT DEFAULT NULL NULL, quantityValue VARCHAR(255) NULL, isFormula BIT DEFAULT 0 NOT NULL, domain VARCHAR(80) NULL, account VARCHAR(80) NULL, startDate date NOT NULL, endDate date DEFAULT NULL NULL, CreatedBy INT NOT NULL, UpdatedBy INT DEFAULT NULL NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, CONSTRAINT PK_AC_CW_VM_MAP PRIMARY KEY (id));

-- changeset redmi:*************-13
CREATE TABLE acusage (Id INT AUTO_INCREMENT NOT NULL, acId VARCHAR(36) NULL, label VARCHAR(40) NULL, usageType VARCHAR(80) NULL, productMapId INT NOT NULL, actualUsage DOUBLE DEFAULT NULL NULL, quantity DOUBLE DEFAULT NULL NULL, unitPrice DOUBLE DEFAULT NULL NULL, month date DEFAULT NULL NULL, CONSTRAINT PK_ACUSAGE PRIMARY KEY (Id));

-- changeset redmi:*************-14
CREATE TABLE acusage_compute (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, totalvcpus DOUBLE NOT NULL, totalramgb DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_COMPUTE PRIMARY KEY (Id));

-- changeset redmi:*************-15
CREATE TABLE acusage_compute_rec (Id INT AUTO_INCREMENT NOT NULL, computeId INT NOT NULL, vmid VARCHAR(36) NULL, name VARCHAR(255) NULL, ostypeid INT DEFAULT NULL NULL, templateid VARCHAR(36) NULL, vcpus DOUBLE DEFAULT NULL NULL, ramgb DOUBLE DEFAULT NULL NULL, hoursused DOUBLE DEFAULT NULL NULL, avgvcpus DOUBLE DEFAULT NULL NULL, avgramgb DOUBLE DEFAULT NULL NULL, startDate date DEFAULT NULL NULL, endDate date DEFAULT NULL NULL, month date DEFAULT NULL NULL, CONSTRAINT PK_ACUSAGE_COMPUTE_REC PRIMARY KEY (Id));

-- changeset redmi:*************-16
CREATE TABLE acusage_ipaddress (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, totalhours DOUBLE NOT NULL, averageips DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_IPADDRESS PRIMARY KEY (Id));

-- changeset redmi:*************-17
CREATE TABLE acusage_license (Id INT AUTO_INCREMENT NOT NULL, computeRecId INT NOT NULL, productMapId INT DEFAULT NULL NULL, quantity DOUBLE DEFAULT NULL NULL, CONSTRAINT PK_ACUSAGE_LICENSE PRIMARY KEY (Id));

-- changeset redmi:*************-18
CREATE TABLE acusage_networkbytes (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, networkgb DOUBLE NOT NULL, networkb DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_NETWORKBYTES PRIMARY KEY (Id));

-- changeset redmi:*************-19
CREATE TABLE acusage_primarystorage (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, gbused DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, HWMDay date NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_PRIMARYSTORAGE PRIMARY KEY (Id));

-- changeset redmi:*************-20
CREATE TABLE acusage_runlog (Id INT AUTO_INCREMENT NOT NULL, command VARCHAR(20) NOT NULL, startTime timestamp DEFAULT NOW() NOT NULL, endTime timestamp DEFAULT NULL NULL, month date NOT NULL, errorCount INT DEFAULT 0 NOT NULL, CONSTRAINT PK_ACUSAGE_RUNLOG PRIMARY KEY (Id));

-- changeset redmi:*************-21
CREATE TABLE acusage_runlog_entry (Id INT AUTO_INCREMENT NOT NULL, runlogId INT NOT NULL, level INT NOT NULL, levelName VARCHAR(20) NOT NULL, createdAt timestamp DEFAULT NOW() NOT NULL, msg VARCHAR(255) NOT NULL, CONSTRAINT PK_ACUSAGE_RUNLOG_ENTRY PRIMARY KEY (Id));

-- changeset redmi:*************-22
CREATE TABLE acusage_secondarystorage (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, gbused DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, HWMDay date NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_SECONDARYSTORAGE PRIMARY KEY (Id));

-- changeset redmi:*************-23
CREATE TABLE contract_signoff (Id INT AUTO_INCREMENT NOT NULL, OrganizationId INT NOT NULL, UserId INT NOT NULL, DocumentId INT NOT NULL, DateSigned datetime NOT NULL, IPAddress VARCHAR(100) NOT NULL, CONSTRAINT PK_CONTRACT_SIGNOFF PRIMARY KEY (Id));

-- changeset redmi:*************-24
CREATE TABLE device_folder (FolderId INT AUTO_INCREMENT NOT NULL, ParentFolderId INT DEFAULT NULL NULL, OrganizationId INT NOT NULL, Name VARCHAR(50) NOT NULL, SortId SMALLINT UNSIGNED DEFAULT 0 NOT NULL, CONSTRAINT PK_DEVICE_FOLDER PRIMARY KEY (FolderId));

-- changeset redmi:*************-25
CREATE TABLE role_available_to (RoleAvailableToId INT AUTO_INCREMENT NOT NULL, Name LONGTEXT NULL, `Description` LONGTEXT NULL, CONSTRAINT PK_ROLE_AVAILABLE_TO PRIMARY KEY (RoleAvailableToId));

-- changeset redmi:*************-26
CREATE TABLE term_documents (Id INT AUTO_INCREMENT NOT NULL, Name VARCHAR(100) NOT NULL, Type INT NOT NULL, Version INT NOT NULL, Content LONGBLOB NOT NULL, CONSTRAINT PK_TERM_DOCUMENTS PRIMARY KEY (Id));

-- changeset redmi:*************-27
ALTER TABLE contract_signoff ADD CONSTRAINT OrganizationId_UserId_DocumentId UNIQUE (OrganizationId, UserId, DocumentId);

-- changeset redmi:*************-28
ALTER TABLE term_documents ADD CONSTRAINT Type_Version UNIQUE (Type, Version);

-- changeset redmi:*************-29
ALTER TABLE acusage ADD CONSTRAINT acId_month_product UNIQUE (acId, month, productMapId);

-- changeset redmi:*************-30
ALTER TABLE acusage_license ADD CONSTRAINT computeRecId UNIQUE (computeRecId, productMapId);

-- changeset redmi:*************-31
ALTER TABLE acusage_compute ADD CONSTRAINT ix_compute_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-32
ALTER TABLE acusage_ipaddress ADD CONSTRAINT ix_ipaddress_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-33
ALTER TABLE acusage_networkbytes ADD CONSTRAINT ix_networkbytes_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-34
ALTER TABLE acusage_primarystorage ADD CONSTRAINT ix_primarystorage_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-35
ALTER TABLE acusage_secondarystorage ADD CONSTRAINT ix_secondarystorage_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-36
CREATE INDEX FK__term_documents ON contract_signoff(DocumentId);

-- changeset redmi:*************-37
CREATE INDEX FK__user ON contract_signoff(UserId);

-- changeset redmi:*************-38
CREATE INDEX FK_device_folder_device_folder ON device_folder(ParentFolderId);

-- changeset redmi:*************-39
CREATE INDEX FK_organization ON device_folder(OrganizationId);

-- changeset redmi:*************-40
CREATE INDEX IX_ConfigurationValues_ConfigurationId ON ConfigurationValues(ConfigurationId);

-- changeset redmi:*************-41
CREATE INDEX IX_Organization_ParentOrganizationId ON `Organization`(ParentOrganizationId);

-- changeset redmi:*************-42
CREATE INDEX IX_User_Organization_Mapping_OrganizationId ON User_Organization_Mapping(OrganizationId);

-- changeset redmi:*************-43
CREATE INDEX IX_User_Organization_Mapping_RoleId ON User_Organization_Mapping(RoleId);

-- changeset redmi:*************-44
CREATE INDEX IX_User_Organization_Mapping_UserId ON User_Organization_Mapping(UserId);

-- changeset redmi:*************-45
CREATE INDEX IX_WhiteLabel_BannerLogo ON WhiteLabel(BannerLogo);

-- changeset redmi:*************-46
CREATE INDEX IX_WhiteLabel_FavIcon ON WhiteLabel(FavIcon);

-- changeset redmi:*************-47
CREATE INDEX IX_WhiteLabel_LoginLogo ON WhiteLabel(LoginLogo);

-- changeset redmi:*************-48
CREATE INDEX IX_WhiteLabel_MainPortalLogo ON WhiteLabel(MainPortalLogo);

-- changeset redmi:*************-49
CREATE INDEX IX_WhiteLabel_NavigationLogo ON WhiteLabel(NavigationLogo);

-- changeset redmi:*************-50
CREATE INDEX IX_WhiteLabel_OrganizationId ON WhiteLabel(OrganizationId);

-- changeset redmi:*************-51
CREATE INDEX computeId ON acusage_compute_rec(computeId);

-- changeset redmi:*************-52
CREATE INDEX fkRunlogId ON acusage_runlog_entry(runlogId);

-- changeset redmi:*************-53
CREATE INDEX productMapId ON ac_cw_vm_map(productMapId);

-- changeset redmi:*************-54
CREATE INDEX productMapId ON acusage(productMapId);

-- changeset redmi:*************-55
ALTER TABLE ConfigurationValues ADD CONSTRAINT FK_ConfigurationValues_Configuration_ConfigurationId FOREIGN KEY (ConfigurationId) REFERENCES Configuration (ConfigurationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-56
ALTER TABLE `Organization` ADD CONSTRAINT FK_Organization_Organization_ParentOrganizationId FOREIGN KEY (ParentOrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-57
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_Organization_OrganizationId FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-58
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_Role_RoleId FOREIGN KEY (RoleId) REFERENCES `Role` (RoleId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-59
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_User_UserId FOREIGN KEY (UserId) REFERENCES User (UserId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-60
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_BannerLogo FOREIGN KEY (BannerLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-61
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_FavIcon FOREIGN KEY (FavIcon) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-62
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_LoginLogo FOREIGN KEY (LoginLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-63
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_MainPortalLogo FOREIGN KEY (MainPortalLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-64
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_NavigationLogo FOREIGN KEY (NavigationLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-65
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Organization_OrganizationId FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-66
ALTER TABLE contract_signoff ADD CONSTRAINT FK__organization FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE NO ACTION ON DELETE NO ACTION;

-- changeset redmi:*************-67
ALTER TABLE contract_signoff ADD CONSTRAINT FK__term_documents FOREIGN KEY (DocumentId) REFERENCES term_documents (Id) ON UPDATE NO ACTION ON DELETE NO ACTION;

-- changeset redmi:*************-68
ALTER TABLE contract_signoff ADD CONSTRAINT FK__user FOREIGN KEY (UserId) REFERENCES User (UserId) ON UPDATE NO ACTION ON DELETE NO ACTION;

-- changeset redmi:*************-69
ALTER TABLE device_folder ADD CONSTRAINT FK_device_folder_device_folder FOREIGN KEY (ParentFolderId) REFERENCES device_folder (FolderId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-70
ALTER TABLE device_folder ADD CONSTRAINT FK_organization FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-71
ALTER TABLE ac_cw_vm_map ADD CONSTRAINT ac_cw_vm_map_ibfk_1 FOREIGN KEY (productMapId) REFERENCES ac_cw_product_map (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-72
ALTER TABLE acusage_compute_rec ADD CONSTRAINT acusage_compute_rec_ibfk_1 FOREIGN KEY (computeId) REFERENCES acusage_compute (Id) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset redmi:*************-73
ALTER TABLE acusage ADD CONSTRAINT acusage_ibfk_1 FOREIGN KEY (productMapId) REFERENCES ac_cw_product_map (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-74
ALTER TABLE acusage_license ADD CONSTRAINT acusage_license_ibfk_1 FOREIGN KEY (computeRecId) REFERENCES acusage_compute_rec (Id) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset redmi:*************-75
ALTER TABLE acusage_runlog_entry ADD CONSTRAINT fkRunlogId FOREIGN KEY (runlogId) REFERENCES acusage_runlog (Id) ON UPDATE RESTRICT ON DELETE CASCADE;

