services:
  backend:
    user: "${UID}:${GID}"
    container_name: "${COMPOSE_PROJECT_NAME}-backend"
    build:
        context: ./
        dockerfile: ./docker/dockerfile
#    image: mcr.microsoft.com/dotnet/aspnet:9.0
    working_dir: /home/<USER>/publish
    command: ./startup.sh
    #build:
    #  context: ./
    #  dockerfile: ./docker/backend/Dockerfile
    #  no_cache: true
    restart: always
    secrets:
      - myacdb-password
      - agentdb-password
      - myaclogsdb-password
      - billingdb-password
    volumes:
      - type: bind
        source: ./
        target: /home/<USER>
      - type: bind
        source: ../../agentdb/agentsetup
        target: /home/<USER>/publish/downloads
        read_only: true
    ports:
      - 127.0.0.1:0:8000
    depends_on:
      myacdb:
        condition: service_healthy
{%- if (project != 'master') and not useLiveAgentDb %}
      agentdb:
        condition: service_healthy
{%- endif %}
      myacdbliquibase:
        condition: service_completed_successfully
      myaclogsdbliquibase:
        condition: service_completed_successfully
      myaclogsdb:
        condition: service_healthy
    networks:
      - default
      - billingdb-net
{%- if (project == 'master') or useLiveAgentDb %}
      - agentapi-net
{%- endif %}
    environment:
      - ASPNETCORE_URLS=http://+:8000
      - ASPNETCORE_ENVIRONMENT=Lab

  worker-services:
    user: "${UID}:${GID}"
    container_name: "${COMPOSE_PROJECT_NAME}-worker-services"
    build:
      context: ./
      dockerfile: ./docker/dockerfile
#    image: mcr.microsoft.com/dotnet/aspnet:9.0
    working_dir: /home/<USER>/services
    command: dotnet MyAdaptiveCloud.WorkerServices.dll
    restart: always
    volumes:
      - type: bind
        source: ./
        target: /home/<USER>
      - type: bind
        source: ./docker/worker-services
        target: /.local/share
    secrets:
      - myacdb-password
      - agentdb-password
    networks:
      - default
{%- if (project == 'master') or useLiveAgentDb %}
      - agentapi-net
{%- endif %}
    depends_on:
{%- if (project != 'master') and not useLiveAgentDb %}
      agentdb:
        condition: service_healthy
{%- endif %}
      myacdb:
        condition: service_healthy
      myacdbliquibase:
        condition: service_completed_successfully
    environment:
      - DOTNET_ENVIRONMENT=lab

  myacdb:
    container_name: "${COMPOSE_PROJECT_NAME}-myacdb"
    image: mariadb:10.5
    command: '--default-authentication-plugin=mysql_native_password'
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'mysqladmin ping -h 127.0.0.1 --password="$$(cat /run/secrets/myacdb-password)" --silent']
      interval: 30s
      retries: 5
      start_period: 30s
    secrets:
      - myacdb-password
    volumes:
      - type: bind
        source: ../../myacdb
        #source: ./docker/myacdb/dump
        target: /docker-entrypoint-initdb.d
    environment:
      - MYSQL_DATABASE=myadaptivecloud
      - MYSQL_ROOT_PASSWORD_FILE=/run/secrets/myacdb-password

{%- if (project != 'master') and not useLiveAgentDb %}
  agentdb:
    container_name: "${COMPOSE_PROJECT_NAME}-agentdb"
    image: mariadb:10-focal
    command: '--default-authentication-plugin=mysql_native_password'
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'mysqladmin ping -h 127.0.0.1 --password="$$(cat /run/secrets/agentdb-password)" --silent']
      interval: 30s
      retries: 5
      start_period: 30s
    secrets:
      - agentdb-password
    volumes:
      - type: bind
        source: ../../agentdb/init
        target: /docker-entrypoint-initdb.d
    environment:
      - MYSQL_DATABASE=acagent
      - MYSQL_ROOT_PASSWORD_FILE=/run/secrets/agentdb-password
{%- endif %}

  myaclogsdb:
    container_name: "${COMPOSE_PROJECT_NAME}-myaclogsdb"
    image: mariadb:10-focal
    command: '--default-authentication-plugin=mysql_native_password'
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'mysqladmin ping -h 127.0.0.1 --password="$$(cat /run/secrets/myaclogsdb-password)" --silent']
      interval: 30s
      retries: 5
      start_period: 30s
    secrets:
      - myaclogsdb-password
    environment:
      - MYSQL_DATABASE=myadaptivecloudlogs
      - MYSQL_ROOT_PASSWORD_FILE=/run/secrets/myaclogsdb-password

  myacdbliquibase:
    image: liquibase/liquibase:4.17
    #command: 'update --changelog-file=changelog.yaml --url="******************************************" --username=root --password="$(cat /run/secrets/myacdb-password)"'
    command: '/liquibase/liquibase --search-path=/liquibase/changelog/ --defaults-file=/run/secrets/liquibase-properties update --contexts="test,main"'
    restart: 'no'

    #configs:
    #  - source: myac-liquibase-config
    #    target: /liquibase/changelog/liquibase.local.properties
    secrets:
      - myacdb-password
      - liquibase-properties
    volumes:
      - type: bind
        source: publish/sql/myadaptivecloud
        target: /liquibase/changelog
    depends_on:
      myacdb:
        condition: service_healthy
  myaclogsdbliquibase:
    image: liquibase/liquibase:4.17
    #command: 'update --changelog-file=changelog.yaml --url="**************************************************" --username=root --password="$(cat /run/secrets/myaclogsdb-password)"'
    command: '/liquibase/liquibase --search-path=/liquibase/changelog/ --defaults-file=/run/secrets/liquibase-logs-properties update'
    restart: 'no'
    #configs:
    #  - source: myaclogs-liquibase-config
    #    target: /liquibase/changelog/liquibase.local.properties
    secrets:
      - myaclogsdb-password
      - liquibase-logs-properties
    volumes:
      - type: bind
        source: publish/sql/myadaptivecloudlogs
        target: /liquibase/changelog
    depends_on:
      myaclogsdb:
        condition: service_healthy

secrets:
  myacdb-password:
    file: docker/myacdb/password.txt
  agentdb-password:
{%- if (project != 'master') and not useLiveAgentDb %}
    file: docker/agentdb/password.txt
{%- else %}
    file: ../../acagent/Docker/password.txt
{%- endif %}
  myaclogsdb-password:
    file: docker/myaclogsdb/password.txt
  liquibase-properties:
    file: docker/myacdb/liquibase.local.properties
  liquibase-logs-properties:
    file: docker/myaclogsdb/liquibase.local.properties
  billingdb-password:
    file: ../../billingdb/password.txt

#configs:
#  myac-liquibase-config:
#    file: docker/myacdb/liquibase.local.properties
#  myaclogs-liquibase-config:
#    file: docker/myaclogsdb/liquibase.local.properties

networks:
  billingdb-net:
    name: billingdb_network
    external: true
{%- if (project == 'master') or useLiveAgentDb %}
  agentapi-net:
    name: acagent_network
    external: true
{%- endif %}

